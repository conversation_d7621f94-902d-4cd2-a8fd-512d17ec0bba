-- STORAGE BUCKET SETUP FOR PRODUCT IMAGES
-- This script sets up the storage bucket and policies for product image uploads

-- ============================================================================
-- STEP 1: CREATE STORAGE BUCKET (if it doesn't exist)
-- ============================================================================

-- Note: Storage buckets are usually created through the Supabase Dashboard
-- But we can try to create it via SQL as well

INSERT INTO storage.buckets (id, name, public)
VALUES ('product-images', 'product-images', true)
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- STEP 2: CREATE STORAGE POLICIES
-- ============================================================================

-- Drop existing storage policies to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view product images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload product images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update product images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete product images" ON storage.objects;
DROP POLICY IF EXISTS "Public can view product images" ON storage.objects;
DROP POLICY IF EXISTS "Admin can upload product images" ON storage.objects;
DROP POLICY IF EXISTS "Admin can delete product images" ON storage.objects;

-- Policy 1: Anyone can view/download product images
CREATE POLICY "Public can view product images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'product-images');

-- Policy 2: Authenticated users can upload product images
CREATE POLICY "Authenticated can upload product images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'product-images');

-- Policy 3: Authenticated users can update product images
CREATE POLICY "Authenticated can update product images"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'product-images')
WITH CHECK (bucket_id = 'product-images');

-- Policy 4: Authenticated users can delete product images
CREATE POLICY "Authenticated can delete product images"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'product-images');

-- ============================================================================
-- STEP 3: GRANT PERMISSIONS ON STORAGE
-- ============================================================================

-- Grant permissions on storage schema
GRANT USAGE ON SCHEMA storage TO anon, authenticated;
GRANT SELECT ON storage.objects TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON storage.objects TO authenticated;

-- ============================================================================
-- STEP 4: VERIFICATION
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '=== STORAGE BUCKET SETUP COMPLETED ===';
  RAISE NOTICE '';
  RAISE NOTICE 'Storage bucket "product-images" is now configured with:';
  RAISE NOTICE '  ✓ Public read access for viewing images';
  RAISE NOTICE '  ✓ Authenticated user upload permissions';
  RAISE NOTICE '  ✓ Authenticated user update permissions';
  RAISE NOTICE '  ✓ Authenticated user delete permissions';
  RAISE NOTICE '';
  RAISE NOTICE 'You can now upload product images through the admin dashboard!';
END $$;

-- ============================================================================
-- ALTERNATIVE: MANUAL STORAGE POLICY CREATION INSTRUCTIONS
-- ============================================================================

/*
If the above SQL doesn't work for storage policies, create them manually:

1. Go to Supabase Dashboard
2. Navigate to Storage > product-images
3. Click on "Policies" tab
4. Create the following policies:

POLICY 1: "Public can view product images"
- Operation: SELECT
- Target roles: public  
- USING expression: true

POLICY 2: "Authenticated can upload product images"
- Operation: INSERT
- Target roles: authenticated
- WITH CHECK expression: true

POLICY 3: "Authenticated can update product images"
- Operation: UPDATE
- Target roles: authenticated
- USING expression: true
- WITH CHECK expression: true

POLICY 4: "Authenticated can delete product images"
- Operation: DELETE
- Target roles: authenticated
- USING expression: true
*/
