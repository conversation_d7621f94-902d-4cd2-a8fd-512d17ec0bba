-- DIAGNOSTIC SCRIPT FOR ORDERS SYSTEM ISSUES
-- Run this to see what's currently wrong with your orders system

-- ============================================================================
-- CHECK 1: TABLE STRUCTURE AND DATA
-- ============================================================================

DO $$
DECLARE
  table_exists BOOLEAN;
  total_users INTEGER;
  total_profiles INTEGER;
  sample_profile RECORD;
BEGIN
  RAISE NOTICE '=== DIAGNOSTIC: TABLE STRUCTURE AND DATA ===';
  
  -- Check if table exists
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'user_profiles'
  ) INTO table_exists;
  
  RAISE NOTICE 'user_profiles table exists: %', table_exists;
  
  IF table_exists THEN
    -- Get counts
    SELECT COUNT(*) INTO total_users FROM auth.users;
    SELECT COUNT(*) INTO total_profiles FROM user_profiles;
    
    RAISE NOTICE 'Total auth users: %', total_users;
    RAISE NOTICE 'Total profiles: %', total_profiles;
    
    -- Show sample profile
    SELECT * INTO sample_profile FROM user_profiles LIMIT 1;
    IF FOUND THEN
      RAISE NOTICE 'Sample profile ID: %', sample_profile.id;
      RAISE NOTICE 'Sample profile name: %', sample_profile.display_name;
      RAISE NOTICE 'Sample profile email: %', sample_profile.email;
    ELSE
      RAISE NOTICE 'No profiles found in table';
    END IF;
  END IF;
END $$;

-- ============================================================================
-- CHECK 2: RLS POLICIES
-- ============================================================================

DO $$
DECLARE
  rls_enabled BOOLEAN;
  policy_count INTEGER;
  policy_rec RECORD;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC: RLS POLICIES ===';
  
  -- Check if RLS is enabled
  SELECT relrowsecurity INTO rls_enabled
  FROM pg_class 
  WHERE relname = 'user_profiles';
  
  RAISE NOTICE 'RLS enabled on user_profiles: %', COALESCE(rls_enabled, false);
  
  -- Count policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'user_profiles';
  
  RAISE NOTICE 'Number of policies: %', policy_count;
  
  -- List all policies
  FOR policy_rec IN 
    SELECT policyname, cmd, roles, qual, with_check 
    FROM pg_policies 
    WHERE tablename = 'user_profiles'
  LOOP
    RAISE NOTICE 'Policy: % (%) for roles: %', policy_rec.policyname, policy_rec.cmd, policy_rec.roles;
  END LOOP;
END $$;

-- ============================================================================
-- CHECK 3: FUNCTIONS
-- ============================================================================

DO $$
DECLARE
  func_rec RECORD;
  func_count INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC: FUNCTIONS ===';
  
  -- Check for profile-related functions
  FOR func_rec IN 
    SELECT proname, pronargs 
    FROM pg_proc 
    WHERE proname LIKE '%user_profile%' OR proname LIKE '%profile%'
    ORDER BY proname
  LOOP
    RAISE NOTICE 'Function: % (% args)', func_rec.proname, func_rec.pronargs;
    func_count := func_count + 1;
  END LOOP;
  
  RAISE NOTICE 'Total profile functions found: %', func_count;
  
  -- Check specific functions the frontend expects
  IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'update_user_profile') THEN
    RAISE NOTICE '✓ update_user_profile exists';
  ELSE
    RAISE NOTICE '✗ update_user_profile MISSING';
  END IF;
  
  IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'update_user_profile_safe') THEN
    RAISE NOTICE '✓ update_user_profile_safe exists';
  ELSE
    RAISE NOTICE '✗ update_user_profile_safe MISSING';
  END IF;
  
  IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'ensure_user_profile_safe') THEN
    RAISE NOTICE '✓ ensure_user_profile_safe exists';
  ELSE
    RAISE NOTICE '✗ ensure_user_profile_safe MISSING';
  END IF;
END $$;

-- ============================================================================
-- CHECK 4: PERMISSIONS
-- ============================================================================

DO $$
DECLARE
  perm_rec RECORD;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC: PERMISSIONS ===';
  
  -- Check table permissions
  FOR perm_rec IN 
    SELECT grantee, privilege_type 
    FROM information_schema.role_table_grants 
    WHERE table_name = 'user_profiles'
  LOOP
    RAISE NOTICE 'Table permission: % can %', perm_rec.grantee, perm_rec.privilege_type;
  END LOOP;
  
  -- Check function permissions
  FOR perm_rec IN 
    SELECT grantee, routine_name 
    FROM information_schema.role_routine_grants 
    WHERE routine_name LIKE '%user_profile%'
  LOOP
    RAISE NOTICE 'Function permission: % can execute %', perm_rec.grantee, perm_rec.routine_name;
  END LOOP;
END $$;

-- ============================================================================
-- CHECK 5: STORAGE BUCKET
-- ============================================================================

DO $$
DECLARE
  bucket_exists BOOLEAN;
  bucket_public BOOLEAN;
  storage_policy_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC: STORAGE ===';
  
  -- Check bucket
  SELECT EXISTS(SELECT 1 FROM storage.buckets WHERE id = 'profile-pictures') INTO bucket_exists;
  RAISE NOTICE 'profile-pictures bucket exists: %', bucket_exists;
  
  IF bucket_exists THEN
    SELECT public INTO bucket_public FROM storage.buckets WHERE id = 'profile-pictures';
    RAISE NOTICE 'Bucket is public: %', bucket_public;
  END IF;
  
  -- Check storage policies
  SELECT COUNT(*) INTO storage_policy_count
  FROM pg_policies 
  WHERE tablename = 'objects' AND policyname LIKE '%profile%';
  
  RAISE NOTICE 'Storage policies for profiles: %', storage_policy_count;
END $$;

-- ============================================================================
-- CHECK 6: TEST CURRENT USER ACCESS
-- ============================================================================

DO $$
DECLARE
  current_user_id UUID;
  can_select BOOLEAN := false;
  can_insert BOOLEAN := false;
  can_update BOOLEAN := false;
  profile_exists BOOLEAN := false;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC: CURRENT USER ACCESS ===';
  
  -- Get current user (if any)
  SELECT auth.uid() INTO current_user_id;
  
  IF current_user_id IS NULL THEN
    RAISE NOTICE 'No authenticated user found';
    RETURN;
  END IF;
  
  RAISE NOTICE 'Current user ID: %', current_user_id;
  
  -- Test if profile exists
  SELECT EXISTS(SELECT 1 FROM user_profiles WHERE id = current_user_id) INTO profile_exists;
  RAISE NOTICE 'Profile exists for current user: %', profile_exists;
  
  -- Test permissions (these might fail due to RLS)
  BEGIN
    PERFORM * FROM user_profiles WHERE id = current_user_id;
    can_select := true;
  EXCEPTION WHEN OTHERS THEN
    can_select := false;
  END;
  
  RAISE NOTICE 'Can SELECT own profile: %', can_select;
  
  -- Test function calls
  IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'ensure_user_profile_safe') THEN
    BEGIN
      PERFORM ensure_user_profile_safe(current_user_id);
      RAISE NOTICE '✓ ensure_user_profile_safe works';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE '✗ ensure_user_profile_safe failed: %', SQLERRM;
    END;
  END IF;
  
  IF EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'update_user_profile') THEN
    BEGIN
      PERFORM update_user_profile(current_user_id, 'Test Name');
      RAISE NOTICE '✓ update_user_profile works';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE '✗ update_user_profile failed: %', SQLERRM;
    END;
  END IF;
END $$;

-- ============================================================================
-- SUMMARY
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== DIAGNOSTIC COMPLETE ===';
  RAISE NOTICE '';
  RAISE NOTICE 'If you see issues above, run fix_profile_system_final.sql';
  RAISE NOTICE 'That script will fix:';
  RAISE NOTICE '  - RLS policy recursion issues';
  RAISE NOTICE '  - Missing functions';
  RAISE NOTICE '  - Permission problems';
  RAISE NOTICE '  - Data persistence issues';
END $$;
