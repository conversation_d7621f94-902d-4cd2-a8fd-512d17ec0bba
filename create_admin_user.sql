-- CREATE ADMIN USER SCRIPT
-- Use this script to make a user an admin after they register

-- ============================================================================
-- INSTRUCTIONS:
-- 1. First, register a user account through your frontend application
-- 2. Copy the user ID from Supabase Dashboard > Authentication > Users
-- 3. Replace 'YOUR_USER_ID_HERE' below with the actual user ID
-- 4. Run this script in Supabase SQL Editor
-- ============================================================================

-- Replace 'YOUR_USER_ID_HERE' with the actual user ID
-- Example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'

DO $$
DECLARE
  target_user_id UUID := 'YOUR_USER_ID_HERE'; -- REPLACE THIS WITH ACTUAL USER ID
  user_exists BOOLEAN;
  profile_exists BOOLEAN;
BEGIN
  -- Validate that the user ID is not the placeholder
  IF target_user_id::TEXT = 'YOUR_USER_ID_HERE' THEN
    RAISE EXCEPTION 'Please replace YOUR_USER_ID_HERE with the actual user ID from Supabase Dashboard';
  END IF;

  -- Check if user exists in auth.users
  SELECT EXISTS (
    SELECT 1 FROM auth.users WHERE id = target_user_id
  ) INTO user_exists;

  IF NOT user_exists THEN
    RAISE EXCEPTION 'User with ID % does not exist in auth.users. Please check the user ID.', target_user_id;
  END IF;

  -- Check if user profile exists
  SELECT EXISTS (
    SELECT 1 FROM user_profiles WHERE id = target_user_id
  ) INTO profile_exists;

  IF NOT profile_exists THEN
    -- Create user profile if it doesn't exist
    INSERT INTO user_profiles (
      id, 
      display_name, 
      email, 
      role, 
      created_at, 
      updated_at
    )
    SELECT 
      target_user_id,
      COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1), 'Admin'),
      email,
      'admin',
      NOW(),
      NOW()
    FROM auth.users 
    WHERE id = target_user_id;
    
    RAISE NOTICE 'Created admin profile for user %', target_user_id;
  ELSE
    -- Update existing profile to admin
    UPDATE user_profiles 
    SET 
      role = 'admin',
      updated_at = NOW()
    WHERE id = target_user_id;
    
    RAISE NOTICE 'Updated user % to admin role', target_user_id;
  END IF;

  -- Verify the change
  DECLARE
    user_role TEXT;
    user_email TEXT;
    user_name TEXT;
  BEGIN
    SELECT role, email, display_name 
    INTO user_role, user_email, user_name
    FROM user_profiles 
    WHERE id = target_user_id;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== ADMIN USER CREATED SUCCESSFULLY ===';
    RAISE NOTICE 'User ID: %', target_user_id;
    RAISE NOTICE 'Email: %', user_email;
    RAISE NOTICE 'Name: %', user_name;
    RAISE NOTICE 'Role: %', user_role;
    RAISE NOTICE '';
    RAISE NOTICE 'The user can now access admin features in the application.';
    RAISE NOTICE 'They may need to log out and log back in to see admin permissions.';
  END;
END $$;

-- ============================================================================
-- ALTERNATIVE: MAKE USER ADMIN BY EMAIL
-- Uncomment and modify the section below if you prefer to use email instead of user ID
-- ============================================================================

/*
DO $$
DECLARE
  target_email TEXT := '<EMAIL>'; -- REPLACE WITH ACTUAL EMAIL
  target_user_id UUID;
  user_exists BOOLEAN;
BEGIN
  -- Find user by email
  SELECT id INTO target_user_id
  FROM auth.users 
  WHERE email = target_email;

  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User with email % does not exist. Please check the email address.', target_email;
  END IF;

  -- Update or create profile
  INSERT INTO user_profiles (
    id, 
    display_name, 
    email, 
    role, 
    created_at, 
    updated_at
  )
  SELECT 
    target_user_id,
    COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1), 'Admin'),
    email,
    'admin',
    NOW(),
    NOW()
  FROM auth.users 
  WHERE id = target_user_id
  ON CONFLICT (id) DO UPDATE SET
    role = 'admin',
    updated_at = NOW();

  RAISE NOTICE 'User % (%) is now an admin', target_email, target_user_id;
END $$;
*/

-- ============================================================================
-- VERIFICATION QUERY
-- Run this to verify admin users in your system
-- ============================================================================

-- Uncomment the query below to see all admin users
/*
SELECT 
  up.id,
  up.email,
  up.display_name,
  up.role,
  up.created_at,
  au.email as auth_email,
  au.created_at as auth_created_at
FROM user_profiles up
JOIN auth.users au ON up.id = au.id
WHERE up.role = 'admin'
ORDER BY up.created_at DESC;
*/
