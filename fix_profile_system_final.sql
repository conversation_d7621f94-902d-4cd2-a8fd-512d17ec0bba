-- FINAL FIX FOR USER PROFILE SYSTEM
-- This script fixes all the issues: RLS recursion, function names, and data persistence

-- ============================================================================
-- STEP 1: DROP ALL EXISTING POLICIES TO PREVENT RECURSION
-- ============================================================================

-- Drop all existing policies on user_profiles
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Public can view profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload own profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own profile pictures" ON storage.objects;

-- ============================================================================
-- STEP 2: CREATE SIMPLE, NON-RECURSIVE RLS POLICIES
-- ============================================================================

-- Enable RLS on user_profiles table
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Policy 1: Users can view their own profile (simple, no recursion)
CREATE POLICY "Users can view own profile"
ON user_profiles FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- Policy 2: Users can insert their own profile
CREATE POLICY "Users can insert own profile"
ON user_profiles FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = id);

-- Policy 3: Users can update their own profile
CREATE POLICY "Users can update own profile"
ON user_profiles FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Policy 4: Allow service role to do everything (for functions)
CREATE POLICY "Service role can manage all profiles"
ON user_profiles FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- ============================================================================
-- STEP 3: CREATE ALL REQUIRED FUNCTIONS WITH CORRECT NAMES
-- ============================================================================

-- Function 1: update_user_profile (the one frontend expects)
CREATE OR REPLACE FUNCTION update_user_profile(
  user_id UUID,
  p_display_name TEXT DEFAULT NULL,
  p_phone TEXT DEFAULT NULL,
  p_dob DATE DEFAULT NULL,
  p_street TEXT DEFAULT NULL,
  p_city TEXT DEFAULT NULL,
  p_state TEXT DEFAULT NULL,
  p_postal_code TEXT DEFAULT NULL,
  p_country TEXT DEFAULT NULL,
  p_avatar_url TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is updating their own profile
  IF user_id != auth.uid() THEN
    RAISE EXCEPTION 'Unauthorized: Cannot update another user''s profile';
  END IF;

  -- Update the profile
  UPDATE user_profiles
  SET
    display_name = COALESCE(p_display_name, display_name),
    phone = COALESCE(p_phone, phone),
    dob = COALESCE(p_dob, dob),
    street = COALESCE(p_street, street),
    city = COALESCE(p_city, city),
    state = COALESCE(p_state, state),
    postal_code = COALESCE(p_postal_code, postal_code),
    country = COALESCE(p_country, country),
    avatar_url = COALESCE(p_avatar_url, avatar_url),
    updated_at = NOW()
  WHERE id = user_id;

  -- Return true if update was successful
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 2: update_user_profile_safe (alternative name)
CREATE OR REPLACE FUNCTION update_user_profile_safe(
  user_id UUID,
  p_display_name TEXT DEFAULT NULL,
  p_phone TEXT DEFAULT NULL,
  p_dob DATE DEFAULT NULL,
  p_street TEXT DEFAULT NULL,
  p_city TEXT DEFAULT NULL,
  p_state TEXT DEFAULT NULL,
  p_postal_code TEXT DEFAULT NULL,
  p_country TEXT DEFAULT NULL,
  p_avatar_url TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Just call the main function
  RETURN update_user_profile(
    user_id, p_display_name, p_phone, p_dob, p_street, 
    p_city, p_state, p_postal_code, p_country, p_avatar_url
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 3: ensure_user_profile_safe
CREATE OR REPLACE FUNCTION ensure_user_profile_safe(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  profile_exists BOOLEAN;
  user_email TEXT;
  user_name TEXT;
BEGIN
  -- Check if profile already exists
  SELECT EXISTS(SELECT 1 FROM user_profiles WHERE id = user_id) INTO profile_exists;
  
  IF profile_exists THEN
    RETURN TRUE;
  END IF;

  -- Get user data from auth.users
  SELECT email, raw_user_meta_data->>'name' 
  INTO user_email, user_name
  FROM auth.users 
  WHERE id = user_id;

  -- Create profile if user exists
  IF user_email IS NOT NULL THEN
    INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
    VALUES (
      user_id,
      COALESCE(user_name, split_part(user_email, '@', 1)),
      user_email,
      'user',
      NOW(),
      NOW()
    );
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 4: get_user_profile (for easy fetching)
CREATE OR REPLACE FUNCTION get_user_profile(user_id UUID)
RETURNS TABLE(
  id UUID,
  display_name TEXT,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  role TEXT,
  phone TEXT,
  dob DATE,
  street TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Ensure profile exists first
  PERFORM ensure_user_profile_safe(user_id);
  
  -- Return the profile
  RETURN QUERY
  SELECT 
    up.id, up.display_name, up.first_name, up.last_name, up.email, up.role,
    up.phone, up.dob, up.street, up.city, up.state, up.postal_code, up.country,
    up.avatar_url, up.created_at, up.updated_at
  FROM user_profiles up
  WHERE up.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 4: GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION update_user_profile(UUID, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_profile_safe(UUID, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION ensure_user_profile_safe(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_profile(UUID) TO authenticated;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE ON user_profiles TO authenticated;
GRANT SELECT ON user_profiles TO anon;

-- ============================================================================
-- STEP 5: CREATE PROFILES FOR EXISTING USERS
-- ============================================================================

-- Create profiles for existing users who don't have one
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  NOW(),
  NOW()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- STEP 6: TEST THE SYSTEM
-- ============================================================================

DO $$
DECLARE
  total_users INTEGER;
  total_profiles INTEGER;
  test_user_id UUID;
  test_result BOOLEAN;
BEGIN
  -- Get statistics
  SELECT COUNT(*) INTO total_users FROM auth.users;
  SELECT COUNT(*) INTO total_profiles FROM user_profiles;

  RAISE NOTICE '=== PROFILE SYSTEM FINAL FIX COMPLETED ===';
  RAISE NOTICE '';
  RAISE NOTICE 'Statistics:';
  RAISE NOTICE '  Total auth users: %', total_users;
  RAISE NOTICE '  Total user profiles: %', total_profiles;
  RAISE NOTICE '';
  
  -- Test function availability
  SELECT id INTO test_user_id FROM auth.users LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Test ensure_user_profile_safe
    SELECT ensure_user_profile_safe(test_user_id) INTO test_result;
    RAISE NOTICE 'Test ensure_user_profile_safe: %', test_result;
    
    -- Test update_user_profile
    SELECT update_user_profile(test_user_id, 'Test Name') INTO test_result;
    RAISE NOTICE 'Test update_user_profile: %', test_result;
  END IF;
  
  RAISE NOTICE '';
  RAISE NOTICE 'Functions created:';
  RAISE NOTICE '  ✓ update_user_profile (main function)';
  RAISE NOTICE '  ✓ update_user_profile_safe (alias)';
  RAISE NOTICE '  ✓ ensure_user_profile_safe';
  RAISE NOTICE '  ✓ get_user_profile';
  RAISE NOTICE '';
  RAISE NOTICE 'RLS Policies:';
  RAISE NOTICE '  ✓ Simple, non-recursive policies';
  RAISE NOTICE '  ✓ Users can only access their own data';
  RAISE NOTICE '  ✓ Service role has full access';
  RAISE NOTICE '';
  RAISE NOTICE '🎉 PROFILE SYSTEM SHOULD NOW WORK!';
  RAISE NOTICE '';
  RAISE NOTICE 'Next steps:';
  RAISE NOTICE '1. Test the profile page in your app';
  RAISE NOTICE '2. Try updating profile information';
  RAISE NOTICE '3. Check that data persists after refresh';
  RAISE NOTICE '4. Set up storage policies manually if needed';
END $$;
