-- FIX PRODUCT RATINGS AND REVIEWS FLOW
-- This script ensures that product ratings are properly updated when reviews are added

-- ============================================================================
-- STEP 1: VERIFY AND FIX PRODUCT_RATINGS_SUMMARY VIEW
-- ============================================================================

-- Drop and recreate the product_ratings_summary view
DROP VIEW IF EXISTS product_ratings_summary CASCADE;

CREATE VIEW product_ratings_summary AS
SELECT 
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

-- Grant permissions on the view
GRANT SELECT ON product_ratings_summary TO anon, authenticated;

-- ============================================================================
-- STEP 2: CREATE/FIX TRIGGER FUNCTION TO UPDATE PRODUCT RATINGS
-- ============================================================================

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS update_product_rating() CASCADE;

-- Create the trigger function
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating NUMERIC;
  review_count BIGINT;
  target_product_id UUID;
BEGIN
  -- Determine which product_id to update
  IF TG_OP = 'DELETE' THEN
    target_product_id := OLD.product_id;
  ELSE
    target_product_id := NEW.product_id;
  END IF;

  -- Calculate the new average rating and review count
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
    COUNT(*)
  INTO
    avg_rating,
    review_count
  FROM product_reviews
  WHERE product_id = target_product_id;

  -- Update the products table with the new rating information
  UPDATE products
  SET
    rating = avg_rating,
    review_count = review_count,
    updated_at = NOW()
  WHERE id = target_product_id;

  -- Log the update for debugging
  RAISE NOTICE 'Updated product % rating to % with % reviews', target_product_id, avg_rating, review_count;

  -- Return appropriate value based on operation
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 3: CREATE TRIGGERS FOR AUTOMATIC RATING UPDATES
-- ============================================================================

-- Drop existing triggers
DROP TRIGGER IF EXISTS update_product_rating_insert ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_update ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_delete ON product_reviews;

-- Create triggers for all operations
CREATE TRIGGER update_product_rating_insert
  AFTER INSERT ON product_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_update
  AFTER UPDATE ON product_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_delete
  AFTER DELETE ON product_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_product_rating();

-- ============================================================================
-- STEP 4: UPDATE EXISTING PRODUCTS WITH CORRECT RATINGS
-- ============================================================================

-- Update all existing products with their current ratings
UPDATE products 
SET 
  rating = COALESCE(summary.average_rating, 0),
  review_count = COALESCE(summary.review_count, 0),
  updated_at = NOW()
FROM (
  SELECT 
    product_id,
    ROUND(AVG(rating)::NUMERIC, 1) AS average_rating,
    COUNT(*) AS review_count
  FROM product_reviews
  GROUP BY product_id
) AS summary
WHERE products.id = summary.product_id;

-- Set rating and review_count to 0 for products with no reviews
UPDATE products 
SET 
  rating = 0,
  review_count = 0,
  updated_at = NOW()
WHERE id NOT IN (
  SELECT DISTINCT product_id 
  FROM product_reviews 
  WHERE product_id IS NOT NULL
);

-- ============================================================================
-- STEP 5: CREATE FUNCTION TO REFRESH PRODUCT RATINGS (MANUAL TRIGGER)
-- ============================================================================

-- Function to manually refresh all product ratings
CREATE OR REPLACE FUNCTION refresh_all_product_ratings()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER := 0;
  product_record RECORD;
BEGIN
  -- Update all products with their current ratings
  FOR product_record IN 
    SELECT DISTINCT p.id as product_id
    FROM products p
  LOOP
    -- Update this specific product
    UPDATE products 
    SET 
      rating = COALESCE((
        SELECT ROUND(AVG(rating)::NUMERIC, 1)
        FROM product_reviews 
        WHERE product_id = product_record.product_id
      ), 0),
      review_count = COALESCE((
        SELECT COUNT(*)
        FROM product_reviews 
        WHERE product_id = product_record.product_id
      ), 0),
      updated_at = NOW()
    WHERE id = product_record.product_id;
    
    updated_count := updated_count + 1;
  END LOOP;

  RAISE NOTICE 'Updated ratings for % products', updated_count;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION refresh_all_product_ratings() TO authenticated;

-- ============================================================================
-- STEP 6: ENSURE PROPER INDEXES FOR PERFORMANCE
-- ============================================================================

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_rating ON product_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_products_rating ON products(rating);
CREATE INDEX IF NOT EXISTS idx_products_review_count ON products(review_count);

-- ============================================================================
-- STEP 7: RUN INITIAL RATING REFRESH
-- ============================================================================

-- Refresh all product ratings immediately
SELECT refresh_all_product_ratings();

-- ============================================================================
-- STEP 8: VERIFICATION AND SUCCESS MESSAGE
-- ============================================================================

DO $$
DECLARE
  total_products INTEGER;
  products_with_ratings INTEGER;
  total_reviews INTEGER;
BEGIN
  -- Get statistics
  SELECT COUNT(*) INTO total_products FROM products;
  SELECT COUNT(*) INTO products_with_ratings FROM products WHERE rating > 0;
  SELECT COUNT(*) INTO total_reviews FROM product_reviews;

  RAISE NOTICE '=== PRODUCT RATINGS FLOW FIX COMPLETED ===';
  RAISE NOTICE '';
  RAISE NOTICE 'Statistics:';
  RAISE NOTICE '  Total products: %', total_products;
  RAISE NOTICE '  Products with ratings: %', products_with_ratings;
  RAISE NOTICE '  Total reviews: %', total_reviews;
  RAISE NOTICE '';
  RAISE NOTICE 'Fixed components:';
  RAISE NOTICE '  ✓ product_ratings_summary view recreated';
  RAISE NOTICE '  ✓ update_product_rating() trigger function created';
  RAISE NOTICE '  ✓ Triggers for INSERT/UPDATE/DELETE created';
  RAISE NOTICE '  ✓ All existing products updated with correct ratings';
  RAISE NOTICE '  ✓ Performance indexes created';
  RAISE NOTICE '  ✓ Manual refresh function available';
  RAISE NOTICE '';
  RAISE NOTICE 'How it works now:';
  RAISE NOTICE '  1. When a user adds a review → rating automatically updates';
  RAISE NOTICE '  2. When a review is edited → rating recalculates';
  RAISE NOTICE '  3. When a review is deleted → rating adjusts';
  RAISE NOTICE '  4. Ratings show immediately on all pages';
  RAISE NOTICE '';
  RAISE NOTICE 'Test the flow:';
  RAISE NOTICE '  1. Add a product review as a user';
  RAISE NOTICE '  2. Check the product detail page';
  RAISE NOTICE '  3. Check the home page featured products';
  RAISE NOTICE '  4. Check the products/shop page';
  RAISE NOTICE '';
  RAISE NOTICE 'Manual refresh command (if needed):';
  RAISE NOTICE '  SELECT refresh_all_product_ratings();';
END $$;
