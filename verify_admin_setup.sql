-- VERIFICATION SCRIPT FOR ADMIN PRODUCT MANAGEMENT
-- Run this script to verify that everything is set up correctly

-- ============================================================================
-- STEP 1: CHECK TABLES EXIST
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '=== CHECKING DATABASE TABLES ===';
  
  -- Check if products table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
    RAISE NOTICE '✓ products table exists';
  ELSE
    RAISE NOTICE '✗ products table missing';
  END IF;
  
  -- Check if categories table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'categories') THEN
    RAISE NOTICE '✓ categories table exists';
  ELSE
    RAISE NOTICE '✗ categories table missing';
  END IF;
  
  -- Check if product_images table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_images') THEN
    RAISE NOTICE '✓ product_images table exists';
  ELSE
    RAISE NOTICE '✗ product_images table missing';
  END IF;
  
  -- Check if user_profiles table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
    RAISE NOTICE '✓ user_profiles table exists';
  ELSE
    RAISE NOTICE '✗ user_profiles table missing';
  END IF;
END $$;

-- ============================================================================
-- STEP 2: CHECK FUNCTIONS EXIST
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CHECKING FUNCTIONS ===';
  
  -- Check if is_admin function exists
  IF EXISTS (SELECT FROM pg_proc WHERE proname = 'is_admin') THEN
    RAISE NOTICE '✓ is_admin() function exists';
  ELSE
    RAISE NOTICE '✗ is_admin() function missing';
  END IF;
  
  -- Check if is_admin_safe function exists
  IF EXISTS (SELECT FROM pg_proc WHERE proname = 'is_admin_safe') THEN
    RAISE NOTICE '✓ is_admin_safe() function exists';
  ELSE
    RAISE NOTICE '✗ is_admin_safe() function missing';
  END IF;
END $$;

-- ============================================================================
-- STEP 3: CHECK RLS POLICIES
-- ============================================================================

DO $$
DECLARE
  policy_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CHECKING RLS POLICIES ===';
  
  -- Check products policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'products';
  
  RAISE NOTICE 'Products table has % RLS policies', policy_count;
  
  -- Check categories policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'categories';
  
  RAISE NOTICE 'Categories table has % RLS policies', policy_count;
  
  -- Check product_images policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'product_images';
  
  RAISE NOTICE 'Product_images table has % RLS policies', policy_count;
END $$;

-- ============================================================================
-- STEP 4: CHECK CATEGORIES DATA
-- ============================================================================

DO $$
DECLARE
  category_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CHECKING CATEGORIES DATA ===';
  
  SELECT COUNT(*) INTO category_count FROM categories;
  
  IF category_count > 0 THEN
    RAISE NOTICE '✓ Categories table has % categories', category_count;
  ELSE
    RAISE NOTICE '✗ Categories table is empty';
  END IF;
END $$;

-- ============================================================================
-- STEP 5: CHECK ADMIN USERS
-- ============================================================================

DO $$
DECLARE
  admin_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CHECKING ADMIN USERS ===';
  
  SELECT COUNT(*) INTO admin_count 
  FROM user_profiles 
  WHERE role = 'admin';
  
  IF admin_count > 0 THEN
    RAISE NOTICE '✓ Found % admin user(s)', admin_count;
  ELSE
    RAISE NOTICE '✗ No admin users found';
    RAISE NOTICE 'Use create_admin_user.sql to create an admin user';
  END IF;
END $$;

-- ============================================================================
-- STEP 6: CHECK STORAGE BUCKET
-- ============================================================================

DO $$
DECLARE
  bucket_exists BOOLEAN;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CHECKING STORAGE BUCKET ===';
  
  SELECT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'product-images'
  ) INTO bucket_exists;
  
  IF bucket_exists THEN
    RAISE NOTICE '✓ product-images storage bucket exists';
  ELSE
    RAISE NOTICE '✗ product-images storage bucket missing';
    RAISE NOTICE 'Run setup_storage_bucket.sql or create it manually';
  END IF;
END $$;

-- ============================================================================
-- STEP 7: LIST CURRENT POLICIES (for debugging)
-- ============================================================================

-- Uncomment the queries below if you want to see detailed policy information

/*
-- Show all policies on products table
SELECT 
  policyname,
  cmd,
  roles,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'products'
ORDER BY policyname;

-- Show all policies on categories table
SELECT 
  policyname,
  cmd,
  roles,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'categories'
ORDER BY policyname;

-- Show all policies on product_images table
SELECT 
  policyname,
  cmd,
  roles,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'product_images'
ORDER BY policyname;

-- Show all categories
SELECT id, name, description FROM categories ORDER BY name;

-- Show all admin users
SELECT id, display_name, email, role, created_at 
FROM user_profiles 
WHERE role = 'admin'
ORDER BY created_at;
*/

-- ============================================================================
-- FINAL MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== VERIFICATION COMPLETE ===';
  RAISE NOTICE '';
  RAISE NOTICE 'If all items show ✓, your admin product management should work!';
  RAISE NOTICE 'If any items show ✗, run the corresponding fix scripts:';
  RAISE NOTICE '  - fix_admin_product_management.sql (main fix)';
  RAISE NOTICE '  - setup_storage_bucket.sql (for storage issues)';
  RAISE NOTICE '  - create_admin_user.sql (to create admin users)';
  RAISE NOTICE '';
  RAISE NOTICE 'Test your admin dashboard now by:';
  RAISE NOTICE '1. Logging in as an admin user';
  RAISE NOTICE '2. Going to /admin/products';
  RAISE NOTICE '3. Trying to create a new product';
  RAISE NOTICE '4. Uploading a product image';
END $$;
