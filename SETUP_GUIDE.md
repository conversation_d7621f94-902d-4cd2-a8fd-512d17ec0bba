# 🚀 The Badhees - Complete Setup Guide

This guide will help you set up **The Badhees** e-commerce application with Supabase and Razorpay integration.

## 📋 Prerequisites

- Node.js 18+ installed
- Git installed
- A Supabase account (free tier available)
- A Razorpay account (for payment processing)

## 🔧 Step 1: Environment Configuration

### 1.1 Create Environment File
```bash
# Copy the example file to create your environment file
cp .env.example .env
```

### 1.2 Configure Supabase

1. **Create a Supabase Project:**
   - Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Click "New Project"
   - Choose your organization and enter project details
   - Wait for the project to be created (2-3 minutes)

2. **Get Your Supabase Credentials:**
   - Go to Settings → API in your Supabase dashboard
   - Copy the **Project URL** and **anon/public key**

3. **Update your .env file:**
   ```env
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your_actual_anon_key_here
   ```

### 1.3 Configure Razorpay

1. **Create a Razorpay Account:**
   - Go to [https://dashboard.razorpay.com/](https://dashboard.razorpay.com/)
   - Sign up for an account
   - Complete the verification process

2. **Get Your Razorpay Keys:**
   - Go to Settings → API Keys
   - Generate Test Keys (for development)
   - Copy the Key ID and Key Secret

3. **Update your .env file:**
   ```env
   VITE_RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
   RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
   RAZORPAY_SECRET=your_actual_secret_key
   ```

## 🗄️ Step 2: Database Setup

### 2.1 Create Database Tables

The application requires several database tables. Run these SQL commands in your Supabase SQL Editor:

1. **Go to Supabase Dashboard → SQL Editor**
2. **Create a new query**
3. **Copy and paste the following SQL:**

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    display_name TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    avatar_url TEXT,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    category_id UUID REFERENCES categories(id),
    stock_quantity INTEGER DEFAULT 0,
    sku TEXT UNIQUE,
    is_active BOOLEAN DEFAULT true,
    images JSONB DEFAULT '[]',
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2 Set Up Row Level Security (RLS)

```sql
-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Categories policies (public read, admin write)
CREATE POLICY "Anyone can view categories" ON categories
    FOR SELECT USING (true);

CREATE POLICY "Only admins can modify categories" ON categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Products policies (public read, admin write)
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Only admins can modify products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

## 🚀 Step 3: Install Dependencies and Run

### 3.1 Install Dependencies
```bash
npm install
```

### 3.2 Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:8080`

## 👤 Step 4: Create Admin User

### 4.1 Register First User
1. Go to `http://localhost:8080/register`
2. Create your admin account
3. Check your email for verification

### 4.2 Set Admin Role
1. Go to Supabase Dashboard → Authentication → Users
2. Find your user and copy the User ID
3. Go to SQL Editor and run:

```sql
INSERT INTO user_profiles (id, display_name, role)
VALUES ('your-user-id-here', 'Admin User', 'admin')
ON CONFLICT (id) DO UPDATE SET role = 'admin';
```

## 🔧 Step 5: Additional Configuration

### 5.1 Storage Setup (for product images)
1. Go to Supabase Dashboard → Storage
2. Create a new bucket called `product-images`
3. Set it to public
4. Configure upload policies

### 5.2 Email Configuration (Optional)
For transactional emails, you can use Supabase Edge Functions or external services like SendGrid.

## 🧪 Step 6: Testing

### 6.1 Test Basic Functionality
- [ ] User registration and login
- [ ] Admin dashboard access
- [ ] Product creation and management
- [ ] Cart functionality

### 6.2 Test Payment Integration
- [ ] Add products to cart
- [ ] Proceed to checkout
- [ ] Test payment with Razorpay test cards

## 🚀 Step 7: Production Deployment

### 7.1 Environment Variables for Production
Update your hosting platform (Vercel, Netlify, etc.) with:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`
- `VITE_RAZORPAY_KEY_ID`
- `RAZORPAY_KEY_ID`
- `RAZORPAY_SECRET`

### 7.2 Switch to Live Keys
For production, replace test keys with live keys:
- Razorpay: `rzp_live_` instead of `rzp_test_`
- Set `VITE_DEBUG_MODE=false`

## 🆘 Troubleshooting

### Common Issues:
1. **Supabase connection errors**: Check URL and keys
2. **Payment failures**: Verify Razorpay keys and webhook URLs
3. **Database errors**: Ensure RLS policies are correctly set
4. **Build errors**: Check Node.js version (18+)

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Verify environment variables
3. Check Supabase logs
4. Review this setup guide

---

**🎉 Congratulations!** Your e-commerce application is now ready to use!
