-- FIX FUNCTION PERMISSIONS ERROR
-- This script fixes the "function name is not unique" error

-- ============================================================================
-- STEP 1: DROP ALL EXISTING VERSIONS OF THE FUNCTIONS
-- ============================================================================

-- Drop all versions of is_admin function
DROP FUNCTION IF EXISTS is_admin();
DROP FUNCTION IF EXISTS is_admin(UUID);
DROP FUNCTION IF EXISTS is_admin(TEXT);

-- Drop all versions of is_admin_safe function
DROP FUNCTION IF EXISTS is_admin_safe();
DROP FUNCTION IF EXISTS is_admin_safe(UUID);
DROP FUNCTION IF EXISTS is_admin_safe(TEXT);

-- ============================================================================
-- STEP 2: CREATE CLEAN VERSIONS OF THE FUNCTIONS
-- ============================================================================

-- Create is_admin function with UUID parameter
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = user_id AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create is_admin_safe function with no parameters (uses auth.uid())
CREATE OR REPLACE FUNCTION is_admin_safe()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 3: GRANT PERMISSIONS WITH SPECIFIC FUNCTION SIGNATURES
-- ============================================================================

-- Grant execute permissions with specific signatures to avoid ambiguity
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_admin_safe() TO anon, authenticated;

-- ============================================================================
-- STEP 4: VERIFICATION
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '=== FUNCTION PERMISSIONS FIX COMPLETED ===';
  RAISE NOTICE '';
  RAISE NOTICE 'Fixed functions:';
  RAISE NOTICE '  ✓ is_admin(UUID) - checks if specific user is admin';
  RAISE NOTICE '  ✓ is_admin_safe() - checks if current user is admin';
  RAISE NOTICE '';
  RAISE NOTICE 'Permissions granted successfully!';
  RAISE NOTICE 'You can now continue with the rest of the admin setup.';
END $$;
