# Manual Storage Policies Setup for Profile Pictures

If you get permission errors when running the SQL script, you'll need to create the storage policies manually through the Supabase Dashboard.

## Step 1: Create the Storage Bucket

1. Go to your Supabase Dashboard
2. Navigate to **Storage** in the left sidebar
3. Click **"New bucket"**
4. Set bucket name: `profile-pictures`
5. Make it **Public** (check the public checkbox)
6. Click **"Create bucket"**

## Step 2: Create Storage Policies

Go to **Storage** > **profile-pictures** > **Policies** tab and create these 4 policies:

### Policy 1: Public can view profile pictures
- **Policy Name**: `Public can view profile pictures`
- **Allowed Operation**: `SELECT`
- **Target Roles**: `public`
- **USING Expression**: 
  ```sql
  bucket_id = 'profile-pictures'
  ```

### Policy 2: Users can upload own profile pictures
- **Policy Name**: `Users can upload own profile pictures`
- **Allowed Operation**: `INSERT`
- **Target Roles**: `authenticated`
- **WITH CHECK Expression**:
  ```sql
  bucket_id = 'profile-pictures' AND (storage.foldername(name))[1] = auth.uid()::text
  ```

### Policy 3: Users can update own profile pictures
- **Policy Name**: `Users can update own profile pictures`
- **Allowed Operation**: `UPDATE`
- **Target Roles**: `authenticated`
- **USING Expression**:
  ```sql
  bucket_id = 'profile-pictures' AND (storage.foldername(name))[1] = auth.uid()::text
  ```

### Policy 4: Users can delete own profile pictures
- **Policy Name**: `Users can delete own profile pictures`
- **Allowed Operation**: `DELETE`
- **Target Roles**: `authenticated`
- **USING Expression**:
  ```sql
  bucket_id = 'profile-pictures' AND (storage.foldername(name))[1] = auth.uid()::text
  ```

## How These Policies Work

1. **Public View**: Anyone can view profile pictures (since bucket is public)
2. **User Upload**: Users can only upload files to their own folder (folder name = user ID)
3. **User Update**: Users can only update their own profile pictures
4. **User Delete**: Users can only delete their own profile pictures

## File Structure

The profile pictures will be stored like this:
```
profile-pictures/
├── user-id-1/
│   └── avatar-timestamp.jpg
├── user-id-2/
│   └── avatar-timestamp.png
└── user-id-3/
    └── avatar-timestamp.webp
```

This ensures users can only access their own profile pictures while keeping them publicly viewable.

## Testing the Setup

After creating the policies, test by:
1. Logging into your app
2. Going to the profile page
3. Trying to upload a profile picture
4. Checking if the image displays correctly
5. Trying to update/change the profile picture

If everything works, the storage system is properly configured!
