-- FIX FUNCTION DEPENDENCIES ERROR
-- This script handles the dependency issue when dropping is_admin function

-- ============================================================================
-- STEP 1: FIND ALL DEPENDENT OBJECTS
-- ============================================================================

DO $$
DECLARE
  dep_record RECORD;
BEGIN
  RAISE NOTICE '=== FINDING DEPENDENCIES FOR is_admin FUNCTION ===';
  
  -- Find all policies that depend on is_admin function
  FOR dep_record IN 
    SELECT schemaname, tablename, policyname, qual, with_check
    FROM pg_policies 
    WHERE qual LIKE '%is_admin%' OR with_check LIKE '%is_admin%'
  LOOP
    RAISE NOTICE 'Policy: %.% depends on is_admin', dep_record.tablename, dep_record.policyname;
  END LOOP;
END $$;

-- ============================================================================
-- STEP 2: DROP DEPENDENT POLICIES FIRST
-- ============================================================================

-- Drop policies that depend on is_admin function
DROP POLICY IF EXISTS "Admins can manage all reviews" ON product_reviews;
DROP POLICY IF EXISTS "Admins can manage all orders" ON orders;
DROP POLICY IF EXISTS "Admins can manage all order items" ON order_items;
DROP POLICY IF EXISTS "Admins can manage all products" ON products;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON user_profiles;

-- Drop any other policies that might depend on is_admin
DO $$
DECLARE
  policy_record RECORD;
  drop_sql TEXT;
BEGIN
  -- Find and drop all policies that reference is_admin
  FOR policy_record IN 
    SELECT schemaname, tablename, policyname
    FROM pg_policies 
    WHERE qual LIKE '%is_admin(%' OR with_check LIKE '%is_admin(%'
  LOOP
    drop_sql := format('DROP POLICY IF EXISTS "%s" ON %s', policy_record.policyname, policy_record.tablename);
    EXECUTE drop_sql;
    RAISE NOTICE 'Dropped policy: % on %', policy_record.policyname, policy_record.tablename;
  END LOOP;
END $$;

-- ============================================================================
-- STEP 3: NOW SAFELY DROP AND RECREATE FUNCTIONS
-- ============================================================================

-- Now we can safely drop the functions
DROP FUNCTION IF EXISTS is_admin(UUID) CASCADE;
DROP FUNCTION IF EXISTS is_admin_safe() CASCADE;

-- Create is_admin function with UUID parameter
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = user_id AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create is_admin_safe function with no parameters (uses auth.uid())
CREATE OR REPLACE FUNCTION is_admin_safe()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_admin_safe() TO anon, authenticated;

-- ============================================================================
-- STEP 4: RECREATE ESSENTIAL POLICIES
-- ============================================================================

-- Recreate essential policies for product_reviews
CREATE POLICY "admins_manage_all_reviews"
ON product_reviews FOR ALL
TO authenticated
USING (is_admin_safe())
WITH CHECK (is_admin_safe());

-- Recreate essential policies for products
CREATE POLICY "admins_manage_all_products"
ON products FOR ALL
TO authenticated
USING (is_admin_safe())
WITH CHECK (is_admin_safe());

-- Recreate essential policies for categories
CREATE POLICY "admins_manage_all_categories"
ON categories FOR ALL
TO authenticated
USING (is_admin_safe())
WITH CHECK (is_admin_safe());

-- ============================================================================
-- STEP 5: VERIFICATION
-- ============================================================================

DO $$
DECLARE
  func_count INTEGER;
  policy_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== VERIFICATION ===';
  
  -- Check functions exist
  SELECT COUNT(*) INTO func_count FROM pg_proc WHERE proname IN ('is_admin', 'is_admin_safe');
  RAISE NOTICE 'Admin functions created: %', func_count;
  
  -- Check policies recreated
  SELECT COUNT(*) INTO policy_count FROM pg_policies WHERE policyname LIKE '%admin%';
  RAISE NOTICE 'Admin policies recreated: %', policy_count;
  
  RAISE NOTICE '';
  RAISE NOTICE '✓ Function dependencies resolved!';
  RAISE NOTICE 'You can now run fix_orders_system_final.sql';
END $$;
