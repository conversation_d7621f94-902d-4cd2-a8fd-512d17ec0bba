-- TEST PROFILE SYSTEM
-- This script tests if the user profile system is working correctly

-- ============================================================================
-- TEST 1: CHECK TABLE STRUCTURE
-- ============================================================================

DO $$
DECLARE
  table_exists BOOLEAN;
  column_count INTEGER;
BEGIN
  -- Check if user_profiles table exists
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'user_profiles'
  ) INTO table_exists;

  -- Count columns in user_profiles table
  SELECT COUNT(*) INTO column_count
  FROM information_schema.columns 
  WHERE table_name = 'user_profiles';

  RAISE NOTICE '=== TABLE STRUCTURE TEST ===';
  RAISE NOTICE 'user_profiles table exists: %', table_exists;
  RAISE NOTICE 'Number of columns: %', column_count;
  
  IF table_exists AND column_count >= 15 THEN
    RAISE NOTICE '✓ Table structure is correct';
  ELSE
    RAISE NOTICE '✗ Table structure has issues';
  END IF;
END $$;

-- ============================================================================
-- TEST 2: CHECK STORAGE BUCKET
-- ============================================================================

DO $$
DECLARE
  bucket_exists BOOLEAN;
  bucket_public BOOLEAN;
BEGIN
  -- Check if profile-pictures bucket exists and is public
  SELECT EXISTS(SELECT 1 FROM storage.buckets WHERE id = 'profile-pictures') INTO bucket_exists;
  SELECT public INTO bucket_public FROM storage.buckets WHERE id = 'profile-pictures';

  RAISE NOTICE '';
  RAISE NOTICE '=== STORAGE BUCKET TEST ===';
  RAISE NOTICE 'profile-pictures bucket exists: %', bucket_exists;
  RAISE NOTICE 'Bucket is public: %', COALESCE(bucket_public, false);
  
  IF bucket_exists AND bucket_public THEN
    RAISE NOTICE '✓ Storage bucket is configured correctly';
  ELSE
    RAISE NOTICE '✗ Storage bucket has issues';
  END IF;
END $$;

-- ============================================================================
-- TEST 3: CHECK RLS POLICIES
-- ============================================================================

DO $$
DECLARE
  table_rls_enabled BOOLEAN;
  storage_rls_enabled BOOLEAN;
  policy_count INTEGER;
  storage_policy_count INTEGER;
BEGIN
  -- Check if RLS is enabled on user_profiles
  SELECT relrowsecurity INTO table_rls_enabled
  FROM pg_class 
  WHERE relname = 'user_profiles';

  -- Check if RLS is enabled on storage.objects
  SELECT relrowsecurity INTO storage_rls_enabled
  FROM pg_class 
  WHERE relname = 'objects' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'storage');

  -- Count policies on user_profiles
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'user_profiles';

  -- Count storage policies for profile-pictures
  SELECT COUNT(*) INTO storage_policy_count
  FROM pg_policies 
  WHERE tablename = 'objects' AND policyname LIKE '%profile%';

  RAISE NOTICE '';
  RAISE NOTICE '=== RLS POLICIES TEST ===';
  RAISE NOTICE 'user_profiles RLS enabled: %', COALESCE(table_rls_enabled, false);
  RAISE NOTICE 'storage.objects RLS enabled: %', COALESCE(storage_rls_enabled, false);
  RAISE NOTICE 'user_profiles policies count: %', policy_count;
  RAISE NOTICE 'storage profile policies count: %', storage_policy_count;
  
  IF table_rls_enabled AND storage_rls_enabled AND policy_count >= 3 AND storage_policy_count >= 3 THEN
    RAISE NOTICE '✓ RLS policies are configured correctly';
  ELSE
    RAISE NOTICE '✗ RLS policies need attention';
  END IF;
END $$;

-- ============================================================================
-- TEST 4: CHECK FUNCTIONS
-- ============================================================================

DO $$
DECLARE
  update_function_exists BOOLEAN;
  ensure_function_exists BOOLEAN;
  trigger_function_exists BOOLEAN;
BEGIN
  -- Check if required functions exist
  SELECT EXISTS(
    SELECT 1 FROM pg_proc 
    WHERE proname = 'update_user_profile_safe'
  ) INTO update_function_exists;

  SELECT EXISTS(
    SELECT 1 FROM pg_proc 
    WHERE proname = 'ensure_user_profile_safe'
  ) INTO ensure_function_exists;

  SELECT EXISTS(
    SELECT 1 FROM pg_proc 
    WHERE proname = 'update_updated_at_column'
  ) INTO trigger_function_exists;

  RAISE NOTICE '';
  RAISE NOTICE '=== FUNCTIONS TEST ===';
  RAISE NOTICE 'update_user_profile_safe exists: %', update_function_exists;
  RAISE NOTICE 'ensure_user_profile_safe exists: %', ensure_function_exists;
  RAISE NOTICE 'update_updated_at_column exists: %', trigger_function_exists;
  
  IF update_function_exists AND ensure_function_exists AND trigger_function_exists THEN
    RAISE NOTICE '✓ All required functions exist';
  ELSE
    RAISE NOTICE '✗ Some functions are missing';
  END IF;
END $$;

-- ============================================================================
-- TEST 5: CHECK TRIGGERS
-- ============================================================================

DO $$
DECLARE
  profile_trigger_exists BOOLEAN;
  updated_at_trigger_exists BOOLEAN;
BEGIN
  -- Check if triggers exist
  SELECT EXISTS(
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'create_user_profile_trigger'
  ) INTO profile_trigger_exists;

  SELECT EXISTS(
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_user_profiles_updated_at'
  ) INTO updated_at_trigger_exists;

  RAISE NOTICE '';
  RAISE NOTICE '=== TRIGGERS TEST ===';
  RAISE NOTICE 'create_user_profile_trigger exists: %', profile_trigger_exists;
  RAISE NOTICE 'update_user_profiles_updated_at exists: %', updated_at_trigger_exists;
  
  IF profile_trigger_exists AND updated_at_trigger_exists THEN
    RAISE NOTICE '✓ All triggers are configured';
  ELSE
    RAISE NOTICE '✗ Some triggers are missing';
  END IF;
END $$;

-- ============================================================================
-- TEST 6: FINAL SUMMARY
-- ============================================================================

DO $$
DECLARE
  total_users INTEGER;
  total_profiles INTEGER;
  match_percentage NUMERIC;
BEGIN
  -- Get user and profile counts
  SELECT COUNT(*) INTO total_users FROM auth.users;
  SELECT COUNT(*) INTO total_profiles FROM user_profiles;
  
  -- Calculate match percentage
  IF total_users > 0 THEN
    match_percentage := (total_profiles::NUMERIC / total_users::NUMERIC) * 100;
  ELSE
    match_percentage := 0;
  END IF;

  RAISE NOTICE '';
  RAISE NOTICE '=== FINAL SUMMARY ===';
  RAISE NOTICE 'Total auth users: %', total_users;
  RAISE NOTICE 'Total user profiles: %', total_profiles;
  RAISE NOTICE 'Profile coverage: %% %', ROUND(match_percentage, 1);
  RAISE NOTICE '';
  
  IF match_percentage >= 100 THEN
    RAISE NOTICE '🎉 USER PROFILE SYSTEM IS FULLY FUNCTIONAL!';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test the profile page in your app';
    RAISE NOTICE '2. Try updating profile information';
    RAISE NOTICE '3. Test profile picture upload';
    RAISE NOTICE '4. Verify data persistence';
  ELSE
    RAISE NOTICE '⚠️  Profile system needs attention';
    RAISE NOTICE 'Some users may not have profiles created yet';
  END IF;
END $$;
