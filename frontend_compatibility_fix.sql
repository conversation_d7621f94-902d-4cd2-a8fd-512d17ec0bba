-- FRONTEND COMPATIBILITY FIX
-- Run this script if you encounter any frontend compatibility issues after the main fix

-- ============================================================================
-- ADDITIONAL COMPATIBILITY FIXES
-- ============================================================================

-- 1. Ensure product_images table exists (if used by frontend)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'product_images'
  ) THEN
    CREATE TABLE product_images (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      product_id UUID REFERENCES products(id) ON DELETE CASCADE,
      image_url TEXT NOT NULL,
      is_primary BOOLEAN DEFAULT false,
      display_order INTEGER DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX idx_product_images_product_id ON product_images(product_id);
    CREATE INDEX idx_product_images_is_primary ON product_images(is_primary);
    
    -- Enable RLS
    ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;
    
    -- Create policy
    CREATE POLICY "Anyone can view product images"
      ON product_images FOR SELECT
      USING (true);
    
    CREATE POLICY "Admins can manage product images"
      ON product_images FOR ALL
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE user_profiles.id = auth.uid()
          AND user_profiles.role = 'admin'
        )
      );
    
    RAISE NOTICE 'Created product_images table';
  ELSE
    RAISE NOTICE 'product_images table already exists';
  END IF;
END $$;

-- 2. Add missing columns to products table if they don't exist
DO $$
BEGIN
  -- Add images column if it doesn't exist (for backward compatibility)
  BEGIN
    ALTER TABLE products ADD COLUMN IF NOT EXISTS images JSONB DEFAULT '[]';
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'images column may already exist';
  END;
  
  -- Ensure all boolean columns have proper defaults
  BEGIN
    ALTER TABLE products ALTER COLUMN is_sale SET DEFAULT false;
    ALTER TABLE products ALTER COLUMN is_new SET DEFAULT false;
    ALTER TABLE products ALTER COLUMN is_featured SET DEFAULT false;
    ALTER TABLE products ALTER COLUMN customization_available SET DEFAULT false;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Boolean columns may already have defaults';
  END;
END $$;

-- 3. Create a function to get product with all related data (for frontend compatibility)
CREATE OR REPLACE FUNCTION get_product_with_details(product_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  price DECIMAL(10,2),
  sale_price DECIMAL(10,2),
  is_sale BOOLEAN,
  is_new BOOLEAN,
  is_featured BOOLEAN,
  category_id UUID,
  status TEXT,
  stock INTEGER,
  sku TEXT,
  customization_available BOOLEAN,
  specifications JSONB,
  rating DECIMAL(3,2),
  review_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  category_name TEXT,
  average_rating DECIMAL(3,2),
  total_reviews BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.description,
    p.price,
    p.sale_price,
    p.is_sale,
    p.is_new,
    p.is_featured,
    p.category_id,
    p.status,
    p.stock,
    p.sku,
    p.customization_available,
    p.specifications,
    p.rating,
    p.review_count,
    p.created_at,
    p.updated_at,
    c.name as category_name,
    COALESCE(prs.average_rating, 0) as average_rating,
    COALESCE(prs.review_count, 0) as total_reviews
  FROM products p
  LEFT JOIN categories c ON p.category_id = c.id
  LEFT JOIN product_ratings_summary prs ON p.id = prs.product_id
  WHERE p.id = product_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create a function to get user profile with safe defaults
CREATE OR REPLACE FUNCTION get_user_profile_safe(user_id UUID)
RETURNS TABLE (
  id UUID,
  display_name TEXT,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  role TEXT,
  phone TEXT,
  dob DATE,
  street TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(up.id, user_id) as id,
    COALESCE(up.display_name, 'User') as display_name,
    up.first_name,
    up.last_name,
    COALESCE(up.email, au.email) as email,
    COALESCE(up.role, 'user') as role,
    up.phone,
    up.dob,
    up.street,
    up.city,
    up.state,
    up.postal_code,
    up.country,
    up.avatar_url,
    COALESCE(up.created_at, NOW()) as created_at,
    COALESCE(up.updated_at, NOW()) as updated_at
  FROM auth.users au
  LEFT JOIN user_profiles up ON au.id = up.id
  WHERE au.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant permissions on new functions
GRANT EXECUTE ON FUNCTION get_product_with_details TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_profile_safe TO authenticated;

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);

-- 7. Update any existing products to have proper default values
UPDATE products 
SET 
  is_sale = COALESCE(is_sale, false),
  is_new = COALESCE(is_new, false),
  is_featured = COALESCE(is_featured, false),
  customization_available = COALESCE(customization_available, false),
  rating = COALESCE(rating, 0),
  review_count = COALESCE(review_count, 0),
  stock = COALESCE(stock, 0),
  specifications = COALESCE(specifications, '{}'),
  status = COALESCE(status, 'active')
WHERE 
  is_sale IS NULL OR 
  is_new IS NULL OR 
  is_featured IS NULL OR 
  customization_available IS NULL OR 
  rating IS NULL OR 
  review_count IS NULL OR 
  stock IS NULL OR 
  specifications IS NULL OR 
  status IS NULL;

-- 8. Ensure all user profiles have proper default values
UPDATE user_profiles 
SET 
  role = COALESCE(role, 'user'),
  display_name = COALESCE(display_name, 'User')
WHERE 
  role IS NULL OR 
  display_name IS NULL;

-- Success message
DO $$
BEGIN
  RAISE NOTICE '=== FRONTEND COMPATIBILITY FIX COMPLETED ===';
  RAISE NOTICE 'Additional functions and indexes created for better frontend compatibility';
  RAISE NOTICE 'All default values have been set properly';
  RAISE NOTICE 'Your application should now work smoothly with the frontend';
END $$;
