# Fix Admin Product Management Issues

## 🚨 Problem Summary

Your admin dashboard is unable to manage products due to several database issues:

1. **Multiple Conflicting RLS Policies** - You have 9 different policies on the products table that conflict with each other
2. **Missing Functions** - Policies reference `is_admin()` and `is_admin_safe()` functions that don't exist
3. **Storage Bucket Issues** - Product image uploads may be blocked due to missing storage policies
4. **Missing Tables** - `product_images` table may not exist or have proper structure
5. **Category Issues** - Categories table may be empty or have policy problems

## 🔧 Solution Files Created

I've created specific scripts to fix each issue:

### 1. **`fix_admin_product_management.sql`** ⭐ **MAIN FIX**
- Removes all conflicting RLS policies
- Creates missing helper functions
- Sets up clean, working policies
- Ensures all required tables exist
- Adds default categories

### 2. **`setup_storage_bucket.sql`** 📁 **STORAGE FIX**
- Creates product-images storage bucket
- Sets up storage policies for image uploads
- Grants proper permissions

### 3. **`verify_admin_setup.sql`** ✅ **VERIFICATION**
- Tests if everything is working
- Shows detailed status of all components
- Helps debug remaining issues

### 4. **`create_admin_user.sql`** 👑 **ADMIN SETUP**
- Creates admin users (if you haven't already)

## 📋 Step-by-Step Fix Instructions

### **STEP 1: Run Main Fix Script**

1. **Open Supabase Dashboard**
   - Go to your Supabase project
   - Navigate to **SQL Editor**

2. **Execute Main Fix**
   - Copy the entire content of `fix_admin_product_management.sql`
   - Paste it into the SQL Editor
   - Click **Run**
   - Wait for success messages

### **STEP 2: Set Up Storage Bucket**

1. **Try SQL Method First**
   - Copy content of `setup_storage_bucket.sql`
   - Run it in SQL Editor

2. **If SQL Method Fails, Use Manual Method**
   - Go to **Storage** in Supabase Dashboard
   - Create bucket named `product-images` (set as public)
   - Go to **Policies** tab in the bucket
   - Create these 4 policies:

   **Policy 1: "Public can view product images"**
   - Operation: `SELECT`
   - Target roles: `public`
   - USING expression: `true`

   **Policy 2: "Authenticated can upload product images"**
   - Operation: `INSERT`
   - Target roles: `authenticated`
   - WITH CHECK expression: `true`

   **Policy 3: "Authenticated can update product images"**
   - Operation: `UPDATE`
   - Target roles: `authenticated`
   - USING expression: `true`
   - WITH CHECK expression: `true`

   **Policy 4: "Authenticated can delete product images"**
   - Operation: `DELETE`
   - Target roles: `authenticated`
   - USING expression: `true`

### **STEP 3: Verify Everything Works**

1. **Run Verification Script**
   - Copy content of `verify_admin_setup.sql`
   - Run it in SQL Editor
   - Check that all items show ✓

2. **Create Admin User (if needed)**
   - If verification shows "No admin users found"
   - Use `create_admin_user.sql` to create one

### **STEP 4: Test Admin Dashboard**

1. **Login as Admin**
   - Use your admin account credentials

2. **Test Product Management**
   - Go to `/admin/products`
   - Try creating a new product
   - Upload a product image
   - Edit an existing product
   - Delete a test product

## 🔍 What Each Fix Does

### **Main Fix Script:**
- ✅ Removes 9 conflicting RLS policies
- ✅ Creates `is_admin()` and `is_admin_safe()` functions
- ✅ Creates 3 clean, working policies:
  - Public can view active products
  - Authenticated users can view all products
  - Admins can manage all products
- ✅ Ensures `product_images` table exists
- ✅ Fixes categories table policies
- ✅ Adds 7 default categories
- ✅ Grants all necessary permissions

### **Storage Fix Script:**
- ✅ Creates `product-images` storage bucket
- ✅ Sets up 4 storage policies for image management
- ✅ Grants storage permissions

### **Verification Script:**
- ✅ Checks all tables exist
- ✅ Verifies functions are created
- ✅ Counts RLS policies
- ✅ Checks for categories data
- ✅ Finds admin users
- ✅ Verifies storage bucket

## 🚨 Troubleshooting

### **Issue**: Still getting permission errors
**Solution**: 
1. Run verification script to see what's missing
2. Make sure you're logged in as an admin user
3. Check browser console for specific error messages

### **Issue**: Image uploads not working
**Solution**:
1. Verify storage bucket exists and is public
2. Check storage policies are created correctly
3. Try manual storage policy creation method

### **Issue**: No categories showing in product form
**Solution**:
1. Check if categories were inserted by running:
   ```sql
   SELECT * FROM categories;
   ```
2. If empty, manually insert categories or re-run main fix script

### **Issue**: "Function does not exist" errors
**Solution**:
1. Re-run the main fix script completely
2. Check if functions were created:
   ```sql
   SELECT proname FROM pg_proc WHERE proname IN ('is_admin', 'is_admin_safe');
   ```

## 🎉 Expected Results

After running all fixes, you should be able to:

- ✅ **Access admin dashboard** without permission errors
- ✅ **View all products** in admin products page
- ✅ **Create new products** with all fields working
- ✅ **Upload product images** successfully
- ✅ **Edit existing products** without issues
- ✅ **Delete products** when needed
- ✅ **Manage categories** if needed
- ✅ **See proper product listings** with images

## 📞 Support

If you still have issues after running all scripts:

1. **Run the verification script** and share the output
2. **Check browser console** for JavaScript errors
3. **Check Supabase logs** for database errors
4. **Share the specific error message** you're seeing

The fix addresses all the conflicting policies and missing components that were preventing your admin product management from working!
