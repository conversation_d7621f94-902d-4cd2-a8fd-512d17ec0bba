-- FIX ADMIN PRODUCT MANAGEMENT ISSUES
-- This script fixes all issues preventing admin users from managing products

-- ============================================================================
-- STEP 1: DROP ALL CONFLICTING POLICIES ON PRODUCTS TABLE
-- ============================================================================

-- Drop all existing policies on products table to start fresh
DROP POLICY IF EXISTS "Admins can delete products" ON products;
DROP POLICY IF EXISTS "Admins can insert products" ON products;
DROP POLICY IF EXISTS "Admins can manage products" ON products;
DROP POLICY IF EXISTS "Admins can update products" ON products;
DROP POLICY IF EXISTS "Ad<PERSON> can view all products" ON products;
DROP POLICY IF EXISTS "Anyone can view products" ON products;
DROP POLICY IF EXISTS "Public can view active products" ON products;
DROP POLICY IF EXISTS "products_admin_all" ON products;
DROP POLICY IF EXISTS "products_public_select" ON products;

-- ============================================================================
-- STEP 2: CREATE MISSING HELPER FUNCTIONS
-- ============================================================================

-- Create is_admin function (referenced by existing policies)
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = user_id AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create is_admin_safe function (referenced by existing policies)
CREATE OR REPLACE FUNCTION is_admin_safe()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 3: CREATE CLEAN, WORKING RLS POLICIES FOR PRODUCTS
-- ============================================================================

-- Public can view active products
CREATE POLICY "Public can view active products"
  ON products FOR SELECT
  TO public
  USING (status = 'active');

-- Authenticated users can view all products (for admin dashboard)
CREATE POLICY "Authenticated can view products"
  ON products FOR SELECT
  TO authenticated
  USING (true);

-- Admins can do everything with products
CREATE POLICY "Admins can manage all products"
  ON products FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- ============================================================================
-- STEP 4: ENSURE PRODUCT_IMAGES TABLE EXISTS AND HAS PROPER POLICIES
-- ============================================================================

-- Create product_images table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'product_images'
  ) THEN
    CREATE TABLE product_images (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      product_id UUID REFERENCES products(id) ON DELETE CASCADE,
      image_url TEXT NOT NULL,
      is_primary BOOLEAN DEFAULT false,
      display_order INTEGER DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX idx_product_images_product_id ON product_images(product_id);
    CREATE INDEX idx_product_images_is_primary ON product_images(is_primary);
    
    RAISE NOTICE 'Created product_images table';
  ELSE
    RAISE NOTICE 'product_images table already exists';
  END IF;
END $$;

-- Enable RLS on product_images
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;

-- Drop existing policies on product_images
DROP POLICY IF EXISTS "Anyone can view product images" ON product_images;
DROP POLICY IF EXISTS "Admins can manage product images" ON product_images;
DROP POLICY IF EXISTS "Public can view product images" ON product_images;

-- Create policies for product_images
CREATE POLICY "Public can view product images"
  ON product_images FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Admins can manage product images"
  ON product_images FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- ============================================================================
-- STEP 5: ENSURE CATEGORIES TABLE HAS PROPER POLICIES
-- ============================================================================

-- Enable RLS on categories if not already enabled
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies on categories
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;
DROP POLICY IF EXISTS "Public can view categories" ON categories;

-- Create policies for categories
CREATE POLICY "Public can view categories"
  ON categories FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Admins can manage categories"
  ON categories FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- ============================================================================
-- STEP 6: INSERT DEFAULT CATEGORIES IF NONE EXIST
-- ============================================================================

-- Insert default categories if the table is empty
INSERT INTO categories (name, description) VALUES
  ('Sofas', 'Comfortable seating solutions for your living room'),
  ('Chairs', 'Stylish and ergonomic chairs for every space'),
  ('Tables', 'Functional and beautiful tables for dining and work'),
  ('Beds', 'Comfortable beds for a good night''s sleep'),
  ('Storage', 'Smart storage solutions for organization'),
  ('Lighting', 'Beautiful lighting fixtures to brighten your space'),
  ('Decor', 'Decorative items to personalize your home')
ON CONFLICT (name) DO NOTHING;

-- ============================================================================
-- STEP 7: GRANT NECESSARY PERMISSIONS
-- ============================================================================

-- Grant permissions on tables
GRANT SELECT ON products TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON products TO authenticated;

GRANT SELECT ON categories TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON categories TO authenticated;

GRANT SELECT ON product_images TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON product_images TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION is_admin TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_admin_safe TO anon, authenticated;

-- ============================================================================
-- STEP 8: CREATE STORAGE BUCKET POLICIES (if needed)
-- ============================================================================

-- Note: Storage bucket policies need to be created in the Supabase Dashboard
-- Go to Storage > Policies and create these policies:

-- Policy Name: "Anyone can view product images"
-- Allowed operation: SELECT
-- Target roles: public
-- USING expression: true

-- Policy Name: "Authenticated users can upload product images"  
-- Allowed operation: INSERT
-- Target roles: authenticated
-- WITH CHECK expression: true

-- Policy Name: "Authenticated users can update product images"
-- Allowed operation: UPDATE  
-- Target roles: authenticated
-- USING expression: true
-- WITH CHECK expression: true

-- Policy Name: "Authenticated users can delete product images"
-- Allowed operation: DELETE
-- Target roles: authenticated
-- USING expression: true

-- ============================================================================
-- STEP 9: VERIFICATION AND SUCCESS MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '=== ADMIN PRODUCT MANAGEMENT FIX COMPLETED ===';
  RAISE NOTICE '';
  RAISE NOTICE 'Fixed Issues:';
  RAISE NOTICE '  ✓ Removed conflicting RLS policies on products table';
  RAISE NOTICE '  ✓ Created missing helper functions (is_admin, is_admin_safe)';
  RAISE NOTICE '  ✓ Created clean, working RLS policies';
  RAISE NOTICE '  ✓ Ensured product_images table exists with proper policies';
  RAISE NOTICE '  ✓ Fixed categories table policies';
  RAISE NOTICE '  ✓ Added default categories';
  RAISE NOTICE '  ✓ Granted necessary permissions';
  RAISE NOTICE '';
  RAISE NOTICE 'What you can now do:';
  RAISE NOTICE '  ✓ Create new products in admin dashboard';
  RAISE NOTICE '  ✓ Edit existing products';
  RAISE NOTICE '  ✓ Upload product images';
  RAISE NOTICE '  ✓ Delete products';
  RAISE NOTICE '  ✓ Manage categories';
  RAISE NOTICE '';
  RAISE NOTICE 'IMPORTANT: You still need to create storage bucket policies';
  RAISE NOTICE 'Go to Supabase Dashboard > Storage > product-images > Policies';
  RAISE NOTICE 'and create the policies mentioned in Step 8 above.';
  RAISE NOTICE '';
  RAISE NOTICE 'Test your admin dashboard now!';
END $$;
